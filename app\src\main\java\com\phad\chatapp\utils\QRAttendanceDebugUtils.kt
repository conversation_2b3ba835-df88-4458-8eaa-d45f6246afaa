package com.phad.chatapp.utils

import android.util.Log
import com.phad.chatapp.models.QRAttendanceData
import com.phad.chatapp.services.QRAttendanceService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Debug utilities for QR attendance system
 * Helps diagnose issues with QR code generation and validation
 */
object QRAttendanceDebugUtils {
    private const val TAG = "QRAttendanceDebug"
    
    /**
     * Test QR code generation and validation flow
     */
    suspend fun testQRFlow(
        sessionId: String,
        eventId: String,
        adminId: String,
        studentId: String,
        qrService: QRAttendanceService
    ): TestResult = withContext(Dispatchers.Default) {
        val results = mutableListOf<String>()
        var success = true
        
        try {
            results.add("=== QR Attendance Flow Test ===")
            results.add("Session ID: $sessionId")
            results.add("Event ID: $eventId")
            results.add("Admin ID: $adminId")
            results.add("Student ID: $studentId")
            results.add("")
            
            // Step 1: Register session
            results.add("Step 1: Registering session...")
            qrService.registerSession(sessionId, adminId, eventId)
            results.add("✓ Session registered successfully")
            results.add("")
            
            // Step 2: Generate QR code
            results.add("Step 2: Generating QR code...")
            val qrData = QRAttendanceData.create(sessionId, eventId, adminId)
            results.add("✓ QR data created:")
            results.add("  - QR ID: ${qrData.qrId}")
            results.add("  - Timestamp: ${qrData.timestamp}")
            results.add("  - Age: ${qrData.getAgeInSeconds()}s")
            results.add("  - Valid: ${qrData.isValid()}")
            results.add("  - Integrity: ${qrData.isIntegrityValid()}")
            results.add("  - Data Complete: ${qrData.isDataComplete()}")
            results.add("")
            
            // Step 3: Convert to JSON
            results.add("Step 3: Converting to JSON...")
            val qrJson = qrData.toJson()
            results.add("✓ JSON generated (${qrJson.length} chars)")
            results.add("JSON preview: ${qrJson.take(100)}...")
            results.add("")
            
            // Step 4: Parse JSON back
            results.add("Step 4: Parsing JSON back...")
            val parsedData = QRAttendanceData.fromJson(qrJson)
            if (parsedData != null) {
                results.add("✓ JSON parsed successfully")
                results.add("  - Matches original: ${parsedData == qrData}")
            } else {
                results.add("✗ Failed to parse JSON")
                success = false
            }
            results.add("")
            
            // Step 5: Validate QR code
            results.add("Step 5: Validating QR code...")
            val validationResult = qrService.validateQRCode(qrJson, studentId)
            if (validationResult.isSuccess) {
                val result = validationResult.getOrNull()!!
                results.add("✓ Validation completed")
                results.add("  - Valid: ${result.isValid}")
                results.add("  - Reason: ${result.reason}")
                if (!result.isValid) {
                    success = false
                }
            } else {
                results.add("✗ Validation failed: ${validationResult.exceptionOrNull()?.message}")
                success = false
            }
            results.add("")
            
            // Step 6: Generate bitmap
            results.add("Step 6: Generating QR bitmap...")
            val bitmapResult = qrService.generateQRCodeBitmap(qrData)
            if (bitmapResult.isSuccess) {
                val bitmap = bitmapResult.getOrNull()!!
                results.add("✓ Bitmap generated successfully")
                results.add("  - Size: ${bitmap.width}x${bitmap.height}")
                results.add("  - Config: ${bitmap.config}")
            } else {
                results.add("✗ Bitmap generation failed: ${bitmapResult.exceptionOrNull()?.message}")
                success = false
            }
            results.add("")
            
            results.add("=== Test Summary ===")
            results.add(if (success) "✓ All tests passed!" else "✗ Some tests failed!")
            
        } catch (e: Exception) {
            results.add("✗ Test failed with exception: ${e.message}")
            Log.e(TAG, "Test failed", e)
            success = false
        }
        
        val report = results.joinToString("\n")
        Log.d(TAG, report)
        
        return@withContext TestResult(success, report)
    }
    
    /**
     * Test session validation specifically
     */
    suspend fun testSessionValidation(
        sessionId: String,
        eventId: String,
        adminId: String,
        qrService: QRAttendanceService
    ): String = withContext(Dispatchers.Default) {
        val results = mutableListOf<String>()
        
        try {
            results.add("=== Session Validation Test ===")
            
            // Register session
            qrService.registerSession(sessionId, adminId, eventId)
            results.add("✓ Session registered")
            
            // Create QR data
            val qrData = QRAttendanceData.create(sessionId, eventId, adminId)
            results.add("✓ QR data created")
            
            // Test validation without expected session
            val validation1 = qrService.validateQRCode(qrData.toJson(), "test_student")
            results.add("Validation without expected session:")
            results.add("  - Success: ${validation1.isSuccess}")
            if (validation1.isSuccess) {
                val result = validation1.getOrNull()!!
                results.add("  - Valid: ${result.isValid}")
                results.add("  - Reason: ${result.reason}")
            }
            
            // Test validation with expected session
            val validation2 = qrService.validateQRCode(qrData.toJson(), "test_student", sessionId)
            results.add("Validation with expected session:")
            results.add("  - Success: ${validation2.isSuccess}")
            if (validation2.isSuccess) {
                val result = validation2.getOrNull()!!
                results.add("  - Valid: ${result.isValid}")
                results.add("  - Reason: ${result.reason}")
            }
            
        } catch (e: Exception) {
            results.add("✗ Test failed: ${e.message}")
            Log.e(TAG, "Session validation test failed", e)
        }
        
        val report = results.joinToString("\n")
        Log.d(TAG, report)
        return@withContext report
    }
    
    /**
     * Log current QR data details
     */
    fun logQRDataDetails(qrData: QRAttendanceData, tag: String = TAG) {
        Log.d(tag, "=== QR Data Details ===")
        Log.d(tag, "Session ID: ${qrData.sessionId}")
        Log.d(tag, "Event ID: ${qrData.eventId}")
        Log.d(tag, "Admin ID: ${qrData.adminId}")
        Log.d(tag, "QR ID: ${qrData.qrId}")
        Log.d(tag, "Timestamp: ${qrData.timestamp}")
        Log.d(tag, "Age: ${qrData.getAgeInSeconds()}s")
        Log.d(tag, "Remaining validity: ${qrData.getRemainingValiditySeconds()}s")
        Log.d(tag, "Is valid: ${qrData.isValid()}")
        Log.d(tag, "Is expired: ${qrData.isExpired()}")
        Log.d(tag, "Integrity valid: ${qrData.isIntegrityValid()}")
        Log.d(tag, "Data complete: ${qrData.isDataComplete()}")
        Log.d(tag, "JSON: ${qrData.toJson()}")
        Log.d(tag, "========================")
    }
    
    data class TestResult(
        val success: Boolean,
        val report: String
    )
}
