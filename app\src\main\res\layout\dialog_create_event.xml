<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="16dp">

    <TextView
        android:id="@+id/titleTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Create Attendance Event"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/eventNameLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:hint="Event Name"
        app:layout_constraintTop_toBottomOf="@id/titleTextView">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/eventNameInput"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:maxLines="1" />

    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/eventDateLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:hint="Event Date"
        app:layout_constraintTop_toBottomOf="@id/eventNameLayout">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/eventDateInput"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="none"
            android:focusable="false"
            android:clickable="true"
            android:maxLines="1" />

    </com.google.android.material.textfield.TextInputLayout>

    <LinearLayout
        android:id="@+id/timePickerLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@id/eventDateLayout">

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/openingTimeLayout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:hint="Opening Time">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/openingTimeInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="none"
                android:focusable="false"
                android:clickable="true"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/closingTimeLayout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:hint="Closing Time">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/closingTimeInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="none"
                android:focusable="false"
                android:clickable="true"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

    </LinearLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/descriptionLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:hint="Description (Optional)"
        app:layout_constraintTop_toBottomOf="@id/timePickerLayout">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/descriptionInput"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textMultiLine"
            android:minLines="2"
            android:maxLines="4" />

    </com.google.android.material.textfield.TextInputLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 