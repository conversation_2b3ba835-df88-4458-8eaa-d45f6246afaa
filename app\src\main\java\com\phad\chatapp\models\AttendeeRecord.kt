package com.phad.chatapp.models

import com.google.firebase.Timestamp
import com.google.firebase.firestore.PropertyName
import com.google.firebase.firestore.Exclude

/**
 * Data class representing admin details who generated the QR code
 */
data class ScannedFromAdmin(
    @PropertyName("admin_roll_number")
    var adminRollNumber: String = "",

    @PropertyName("admin_name")
    var adminName: String = ""
) {
    // Empty constructor for Firestore
    constructor() : this("", "")
}

/**
 * Data model representing an individual attendee record in the consolidated NSS_Events_Attendence collection
 */
data class AttendeeRecord(
    @PropertyName("roll_number")
    var rollNumber: String = "",

    @PropertyName("name")
    var name: String = "",

    @PropertyName("nss_group")
    var nssGroup: String = "",

    @PropertyName("scan_timestamp")
    var scanTimestamp: Timestamp = Timestamp.now(),

    @PropertyName("scanned_from")
    var scannedFrom: ScannedFromAdmin = ScannedFromAdmin(),

    // Legacy fields for backward compatibility
    @PropertyName("qr_code_id")
    var qrCodeId: String = "", // ID of the QR code that was scanned

    @PropertyName("validation_status")
    var validationStatus: String = STATUS_VALID,

    @PropertyName("device_info")
    var deviceInfo: String = "", // Optional: device information for security

    @PropertyName("location_lat")
    var locationLat: Double? = null,

    @PropertyName("location_lng")
    var locationLng: Double? = null,

    @PropertyName("notes")
    var notes: String = ""
) {
    companion object {
        // Validation status constants
        const val STATUS_VALID = "valid"
        const val STATUS_EXPIRED = "expired"
        const val STATUS_INVALID = "invalid"
        const val STATUS_DUPLICATE = "duplicate"
    }
    
    // Empty constructor for Firestore
    constructor() : this("", "", "", Timestamp.now(), ScannedFromAdmin(), "", STATUS_VALID, "", null, null, "")
    
    /**
     * Check if the attendance record is valid
     */
    @Exclude
    fun isValid(): Boolean {
        return validationStatus == STATUS_VALID
    }
    
    /**
     * Check if the attendance was marked recently (within last 5 seconds)
     */
    @Exclude
    fun isRecentScan(): Boolean {
        val currentTime = System.currentTimeMillis()
        val scanTime = scanTimestamp.toDate().time
        val timeDifferenceSeconds = (currentTime - scanTime) / 1000
        return timeDifferenceSeconds <= 5
    }
    
    /**
     * Get formatted scan timestamp
     */
    @Exclude
    fun getFormattedScanTime(): String {
        val date = scanTimestamp.toDate()
        val formatter = java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault())
        return formatter.format(date)
    }
    
    /**
     * Get formatted scan date and time
     */
    @Exclude
    fun getFormattedScanDateTime(): String {
        val date = scanTimestamp.toDate()
        val formatter = java.text.SimpleDateFormat("dd MMM yyyy, HH:mm:ss", java.util.Locale.getDefault())
        return formatter.format(date)
    }
    
    /**
     * Check if location data is available
     */
    @Exclude
    fun hasLocationData(): Boolean {
        return locationLat != null && locationLng != null
    }
    
    /**
     * Get formatted location string
     */
    @Exclude
    fun getFormattedLocation(): String {
        return if (hasLocationData()) {
            "Lat: ${String.format("%.4f", locationLat)}, Long: ${String.format("%.4f", locationLng)}"
        } else {
            "Location not available"
        }
    }
    
    /**
     * Create a copy with updated validation status
     */
    @Exclude
    fun withValidationStatus(status: String): AttendeeRecord {
        return this.copy(validationStatus = status)
    }
    
    /**
     * Create a copy with location data
     */
    @Exclude
    fun withLocation(lat: Double, lng: Double): AttendeeRecord {
        return this.copy(locationLat = lat, locationLng = lng)
    }
    
    /**
     * Validate attendee record data
     */
    @Exclude
    fun isDataValid(): Boolean {
        return rollNumber.isNotBlank() &&
               name.isNotBlank() &&
               nssGroup.isNotBlank() &&
               scannedFrom.adminRollNumber.isNotBlank()
    }

    /**
     * Create attendee record with admin information
     */
    @Exclude
    fun withAdminInfo(adminRollNumber: String, adminName: String): AttendeeRecord {
        return this.copy(
            scannedFrom = ScannedFromAdmin(adminRollNumber, adminName)
        )
    }

    /**
     * Create attendee record with NSS group
     */
    @Exclude
    fun withNssGroup(group: String): AttendeeRecord {
        return this.copy(nssGroup = group)
    }
    
    /**
     * Get validation status display text
     */
    @Exclude
    fun getValidationStatusText(): String {
        return when (validationStatus) {
            STATUS_VALID -> "Valid"
            STATUS_EXPIRED -> "Expired QR Code"
            STATUS_INVALID -> "Invalid QR Code"
            STATUS_DUPLICATE -> "Duplicate Attendance"
            else -> "Unknown Status"
        }
    }
}
