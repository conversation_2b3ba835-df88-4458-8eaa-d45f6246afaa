package com.phad.chatapp.utils

import android.util.Log
import com.phad.chatapp.models.QRAttendanceData
import com.phad.chatapp.models.AttendeeRecord
import com.phad.chatapp.repositories.AttendanceQRRepository
import kotlinx.coroutines.runBlocking
import java.security.MessageDigest
import java.util.concurrent.ConcurrentHashMap

/**
 * Security validator for QR attendance system
 * Provides comprehensive validation and security measures
 */
class QRSecurityValidator {
    private val TAG = "QRSecurityValidator"
    
    companion object {
        // Security constants
        private const val MAX_QR_AGE_MS = 5000L // 5 seconds
        private const val MIN_QR_AGE_MS = 0L // Immediate validity
        private const val MAX_SCAN_ATTEMPTS_PER_MINUTE = 10
        private const val RATE_LIMIT_WINDOW_MS = 60000L // 1 minute
        
        // Singleton instance
        @Volatile
        private var INSTANCE: QRSecurityValidator? = null
        
        fun getInstance(): QRSecurityValidator {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: QRSecurityValidator().also { INSTANCE = it }
            }
        }
    }
    
    // Rate limiting storage
    private val scanAttempts = ConcurrentHashMap<String, MutableList<Long>>()
    
    // Used QR codes cache to prevent replay attacks
    private val usedQRCodes = ConcurrentHashMap<String, Long>()
    
    // Session validation cache
    private val validSessions = ConcurrentHashMap<String, SessionValidationInfo>()

    // Repository for Firestore access
    private val repository = AttendanceQRRepository()
    
    /**
     * Comprehensive QR code validation
     */
    fun validateQRCode(
        qrData: QRAttendanceData,
        studentId: String,
        sessionId: String? = null
    ): ValidationResult {
        Log.d(TAG, "Starting comprehensive QR validation for student: $studentId")
        
        try {
            // 1. Basic data validation
            val basicValidation = validateBasicData(qrData)
            if (!basicValidation.isValid) {
                return basicValidation
            }
            
            // 2. Timestamp validation
            val timestampValidation = validateTimestamp(qrData)
            if (!timestampValidation.isValid) {
                return timestampValidation
            }
            
            // 3. Integrity validation (signature check)
            val integrityValidation = validateIntegrity(qrData)
            if (!integrityValidation.isValid) {
                return integrityValidation
            }
            
            // 4. Replay attack prevention
            val replayValidation = validateReplayAttack(qrData)
            if (!replayValidation.isValid) {
                return replayValidation
            }
            
            // 5. Rate limiting validation
            val rateLimitValidation = validateRateLimit(studentId)
            if (!rateLimitValidation.isValid) {
                return rateLimitValidation
            }
            
            // 6. Session validation
            // Always validate the session from QR data, and optionally check against expected session
            val sessionValidation = if (sessionId != null) {
                // If expected session provided, validate against it
                validateSession(qrData, sessionId)
            } else {
                // Otherwise, validate that the session in QR data is active
                validateQRSession(qrData)
            }
            if (!sessionValidation.isValid) {
                return sessionValidation
            }
            
            // 7. Mark QR as used
            markQRAsUsed(qrData)
            
            // 8. Record scan attempt
            recordScanAttempt(studentId)
            
            Log.d(TAG, "QR validation successful for student: $studentId")
            return ValidationResult(true, "QR code is valid", ValidationResult.VALID)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error during QR validation", e)
            return ValidationResult(false, "Validation error: ${e.message}", ValidationResult.ERROR)
        }
    }
    
    /**
     * Validate basic QR data structure
     */
    private fun validateBasicData(qrData: QRAttendanceData): ValidationResult {
        if (!qrData.isDataComplete()) {
            return ValidationResult(false, "Incomplete QR data", ValidationResult.INVALID_FORMAT)
        }
        
        if (qrData.version != "1.0") {
            return ValidationResult(false, "Unsupported QR version", ValidationResult.INVALID_VERSION)
        }
        
        return ValidationResult(true, "Basic data valid", ValidationResult.VALID)
    }
    
    /**
     * Validate QR code timestamp
     */
    private fun validateTimestamp(qrData: QRAttendanceData): ValidationResult {
        val currentTime = System.currentTimeMillis()
        val qrTime = qrData.timestamp
        val age = currentTime - qrTime
        
        if (age < MIN_QR_AGE_MS) {
            return ValidationResult(false, "QR code is from the future", ValidationResult.INVALID_TIMESTAMP)
        }
        
        if (age > MAX_QR_AGE_MS) {
            return ValidationResult(false, "QR code has expired (${age}ms old)", ValidationResult.EXPIRED)
        }
        
        return ValidationResult(true, "Timestamp valid", ValidationResult.VALID)
    }
    
    /**
     * Validate QR code integrity using signature
     */
    private fun validateIntegrity(qrData: QRAttendanceData): ValidationResult {
        if (!qrData.isIntegrityValid()) {
            return ValidationResult(false, "QR code signature is invalid", ValidationResult.INVALID_SIGNATURE)
        }
        
        return ValidationResult(true, "Integrity valid", ValidationResult.VALID)
    }
    
    /**
     * Prevent replay attacks by checking if QR was already used
     */
    private fun validateReplayAttack(qrData: QRAttendanceData): ValidationResult {
        val qrId = qrData.qrId
        
        if (usedQRCodes.containsKey(qrId)) {
            val usedTime = usedQRCodes[qrId] ?: 0
            val timeSinceUsed = System.currentTimeMillis() - usedTime
            return ValidationResult(
                false, 
                "QR code already used ${timeSinceUsed}ms ago", 
                ValidationResult.REPLAY_ATTACK
            )
        }
        
        return ValidationResult(true, "No replay detected", ValidationResult.VALID)
    }
    
    /**
     * Validate rate limiting to prevent spam
     */
    private fun validateRateLimit(studentId: String): ValidationResult {
        val currentTime = System.currentTimeMillis()
        val attempts = scanAttempts.getOrPut(studentId) { mutableListOf() }
        
        // Remove old attempts outside the window
        attempts.removeAll { currentTime - it > RATE_LIMIT_WINDOW_MS }
        
        if (attempts.size >= MAX_SCAN_ATTEMPTS_PER_MINUTE) {
            return ValidationResult(
                false, 
                "Too many scan attempts. Please wait before trying again.", 
                ValidationResult.RATE_LIMITED
            )
        }
        
        return ValidationResult(true, "Rate limit OK", ValidationResult.VALID)
    }
    
    /**
     * Validate session consistency
     */
    private fun validateSession(qrData: QRAttendanceData, expectedSessionId: String): ValidationResult {
        if (qrData.sessionId != expectedSessionId) {
            return ValidationResult(
                false,
                "QR code is for a different session",
                ValidationResult.INVALID_SESSION
            )
        }

        // First check in-memory cache
        var sessionInfo = validSessions[expectedSessionId]

        // If not found in cache, check Firestore
        if (sessionInfo == null) {
            Log.d(TAG, "Expected session not found in cache, checking Firestore: $expectedSessionId")
            try {
                val firestoreSession = runBlocking {
                    repository.getAttendanceSession(expectedSessionId).getOrNull()
                }

                if (firestoreSession != null && firestoreSession.isSessionActive()) {
                    // Session found in Firestore and is active, add to cache
                    sessionInfo = SessionValidationInfo(
                        sessionId = firestoreSession.id,
                        adminId = firestoreSession.adminId,
                        eventId = firestoreSession.eventId,
                        startTime = firestoreSession.sessionStartTime.toDate().time,
                        endTime = firestoreSession.sessionEndTime?.toDate()?.time,
                        isActive = firestoreSession.isActive
                    )
                    validSessions[expectedSessionId] = sessionInfo
                    Log.d(TAG, "Expected session found in Firestore and added to cache: $expectedSessionId")
                } else {
                    Log.w(TAG, "Expected session not found in Firestore or is inactive: $expectedSessionId")
                    return ValidationResult(
                        false,
                        "Session not found or inactive",
                        ValidationResult.SESSION_NOT_FOUND
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error checking expected session in Firestore: $expectedSessionId", e)
                return ValidationResult(
                    false,
                    "Session validation failed: ${e.message}",
                    ValidationResult.ERROR
                )
            }
        }

        if (!sessionInfo.isActive) {
            return ValidationResult(
                false,
                "Session has ended",
                ValidationResult.SESSION_ENDED
            )
        }

        return ValidationResult(true, "Session valid", ValidationResult.VALID)
    }

    /**
     * Validate that the session in QR data is active (without expected session check)
     */
    private fun validateQRSession(qrData: QRAttendanceData): ValidationResult {
        val sessionId = qrData.sessionId

        // First check in-memory cache
        var sessionInfo = validSessions[sessionId]

        // If not found in cache, check Firestore
        if (sessionInfo == null) {
            Log.d(TAG, "Session not found in cache, checking Firestore: $sessionId")
            try {
                val firestoreSession = runBlocking {
                    repository.getAttendanceSession(sessionId).getOrNull()
                }

                if (firestoreSession != null && firestoreSession.isSessionActive()) {
                    // Session found in Firestore and is active, add to cache
                    sessionInfo = SessionValidationInfo(
                        sessionId = firestoreSession.id,
                        adminId = firestoreSession.adminId,
                        eventId = firestoreSession.eventId,
                        startTime = firestoreSession.sessionStartTime.toDate().time,
                        endTime = firestoreSession.sessionEndTime?.toDate()?.time,
                        isActive = firestoreSession.isActive
                    )
                    validSessions[sessionId] = sessionInfo
                    Log.d(TAG, "Session found in Firestore and added to cache: $sessionId")
                } else {
                    Log.w(TAG, "Session not found in Firestore or is inactive: $sessionId")
                    return ValidationResult(
                        false,
                        "Session not found or inactive",
                        ValidationResult.SESSION_NOT_FOUND
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error checking session in Firestore: $sessionId", e)
                return ValidationResult(
                    false,
                    "Session validation failed: ${e.message}",
                    ValidationResult.ERROR
                )
            }
        }

        if (!sessionInfo.isActive) {
            return ValidationResult(
                false,
                "Session has ended",
                ValidationResult.SESSION_ENDED
            )
        }

        // Verify session data matches QR data
        if (sessionInfo.eventId != qrData.eventId) {
            return ValidationResult(
                false,
                "QR code event doesn't match session",
                ValidationResult.INVALID_SESSION
            )
        }

        if (sessionInfo.adminId != qrData.adminId) {
            return ValidationResult(
                false,
                "QR code admin doesn't match session",
                ValidationResult.INVALID_SESSION
            )
        }

        return ValidationResult(true, "QR session is valid", ValidationResult.VALID)
    }
    
    /**
     * Mark QR code as used to prevent replay
     */
    private fun markQRAsUsed(qrData: QRAttendanceData) {
        usedQRCodes[qrData.qrId] = System.currentTimeMillis()
        
        // Clean up old entries to prevent memory leaks
        cleanupUsedQRCodes()
    }
    
    /**
     * Record scan attempt for rate limiting
     */
    private fun recordScanAttempt(studentId: String) {
        val attempts = scanAttempts.getOrPut(studentId) { mutableListOf() }
        attempts.add(System.currentTimeMillis())
    }
    
    /**
     * Register an active session
     */
    fun registerSession(sessionId: String, adminId: String, eventId: String) {
        validSessions[sessionId] = SessionValidationInfo(
            sessionId = sessionId,
            adminId = adminId,
            eventId = eventId,
            startTime = System.currentTimeMillis(),
            isActive = true
        )
        Log.d(TAG, "Session registered: $sessionId")
    }
    
    /**
     * End a session
     */
    fun endSession(sessionId: String) {
        validSessions[sessionId]?.let { sessionInfo ->
            validSessions[sessionId] = sessionInfo.copy(
                isActive = false,
                endTime = System.currentTimeMillis()
            )
        }
        Log.d(TAG, "Session ended: $sessionId")
    }
    
    /**
     * Check for duplicate attendance in the same session
     */
    fun checkDuplicateAttendance(sessionId: String, studentId: String, existingAttendees: List<AttendeeRecord>): Boolean {
        return existingAttendees.any { it.rollNumber == studentId }
    }
    
    /**
     * Clean up old used QR codes to prevent memory leaks
     */
    private fun cleanupUsedQRCodes() {
        val currentTime = System.currentTimeMillis()
        val cutoffTime = currentTime - (MAX_QR_AGE_MS * 10) // Keep for 10x the validity period
        
        usedQRCodes.entries.removeAll { (_, timestamp) ->
            currentTime - timestamp > cutoffTime
        }
    }
    
    /**
     * Get security statistics
     */
    fun getSecurityStats(): SecurityStats {
        return SecurityStats(
            totalUsedQRCodes = usedQRCodes.size,
            activeSessions = validSessions.values.count { it.isActive },
            totalSessions = validSessions.size,
            studentsWithAttempts = scanAttempts.size
        )
    }
}

/**
 * Data class for validation results
 */
data class ValidationResult(
    val isValid: Boolean,
    val message: String,
    val code: Int
) {
    companion object {
        const val VALID = 0
        const val INVALID_FORMAT = 1
        const val INVALID_VERSION = 2
        const val INVALID_TIMESTAMP = 3
        const val EXPIRED = 4
        const val INVALID_SIGNATURE = 5
        const val REPLAY_ATTACK = 6
        const val RATE_LIMITED = 7
        const val INVALID_SESSION = 8
        const val SESSION_NOT_FOUND = 9
        const val SESSION_ENDED = 10
        const val ERROR = 99
    }
}

/**
 * Data class for session validation info
 */
data class SessionValidationInfo(
    val sessionId: String,
    val adminId: String,
    val eventId: String,
    val startTime: Long,
    val endTime: Long? = null,
    val isActive: Boolean
)

/**
 * Data class for security statistics
 */
data class SecurityStats(
    val totalUsedQRCodes: Int,
    val activeSessions: Int,
    val totalSessions: Int,
    val studentsWithAttempts: Int
)
