package com.phad.chatapp.features.scheduling.schedule

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.FastOutSlowInEasing
import com.phad.chatapp.features.scheduling.ui.theme.NeutralCardSurface

@Composable
fun EditTeachingSlotsScreen(navController: NavController) {
    // Enhanced animation states following UI.md enhanced timing patterns
    var topBarVisible by remember { mutableStateOf(false) }
    var contentVisible by remember { mutableStateOf(false) }
    var cardVisible by remember { mutableStateOf(false) }

    // Animation reset trigger to ensure consistent animations on every screen visit
    var animationTrigger by remember { mutableStateOf(0) }

    // Animation control flag for accessibility compliance
    var animationsEnabled by remember { mutableStateOf(true) }

    // 1.5-second animation timeout that starts after loading completes (UI.md specification)
    LaunchedEffect(Unit) {
        // Start with animations enabled
        animationsEnabled = true

        // Disable animations after 1.5 seconds
        kotlinx.coroutines.delay(1500)
        animationsEnabled = false
    }

    // Enhanced entrance animations with 100ms → 200ms → 100ms timing pattern
    LaunchedEffect(animationTrigger) {
        // Reset all animation states first
        topBarVisible = false
        contentVisible = false
        cardVisible = false

        if (animationsEnabled) {
            kotlinx.coroutines.delay(100)
            topBarVisible = true
            kotlinx.coroutines.delay(200)
            contentVisible = true
            kotlinx.coroutines.delay(100)
            cardVisible = true
        } else {
            // Show all immediately if animations disabled
            topBarVisible = true
            contentVisible = true
            cardVisible = true
        }
    }

    // Increment animation trigger when screen is visited to reset animations
    LaunchedEffect(Unit) {
        animationTrigger++
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black)
    ) {
        // Enhanced content area with standardized UI.md timing
        AnimatedVisibility(
            visible = contentVisible,
            enter = slideInVertically(
                initialOffsetY = { if (animationsEnabled) it / 4 else 0 },
                animationSpec = tween(durationMillis = if (animationsEnabled) 400 else 0)
            ) + fadeIn(
                animationSpec = tween(durationMillis = if (animationsEnabled) 300 else 0)
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                // Top bar with standardized UI.md timing (500ms fadeIn + 100ms delay)
                AnimatedVisibility(
                    visible = topBarVisible,
                    enter = fadeIn(
                        animationSpec = tween(500, delayMillis = if (animationsEnabled) 100 else 0)
                    )
                ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp), // Reduced from 24dp to 16dp
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Back button
                    IconButton(
                        onClick = { navController.navigateUp() },
                        modifier = Modifier.padding(end = 8.dp)
                    ) {
                        Icon(
                            Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back",
                            tint = Color.White
                        )
                    }

                    // Title
                    Text(
                        "Edit Teaching Slots",
                        style = MaterialTheme.typography.titleLarge,
                        color = Color.White,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.weight(1f)
                    )
                }
            }

            // Main card with entrance animation as per UI.md specifications
            AnimatedVisibility(
                visible = cardVisible,
                enter = slideInVertically(
                    initialOffsetY = { if (animationsEnabled) it / 2 else 0 },
                    animationSpec = tween(durationMillis = if (animationsEnabled) 300 else 0)
                ) + fadeIn(
                    animationSpec = tween(durationMillis = if (animationsEnabled) 250 else 0)
                )
            ) {
                // Animated background color transition following UI.md specifications
                val animatedCardColor by animateColorAsState(
                    targetValue = NeutralCardSurface,
                    animationSpec = tween(
                        durationMillis = if (animationsEnabled) 300 else 0,
                        easing = FastOutSlowInEasing
                    ),
                    label = "card_background_color"
                )

                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = animatedCardColor
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(24.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            "Edit Teaching Slots",
                            style = MaterialTheme.typography.titleMedium,
                            color = Color.White,
                            fontWeight = FontWeight.Medium,
                            textAlign = TextAlign.Center
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        Text(
                            "This screen allows you to modify existing teaching slot presets",
                            style = MaterialTheme.typography.bodyMedium,
                            color = Color.Gray,
                            textAlign = TextAlign.Center
                        )

                        Spacer(modifier = Modifier.height(24.dp))

                        Button(
                            onClick = { navController.navigateUp() },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFFFFD600)
                            ),
                            shape = RoundedCornerShape(8.dp)
                        ) {
                            Text(
                                "Go Back",
                                color = Color.Black,
                                fontWeight = FontWeight.Medium,
                                modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp)
                            )
                        }
                    }
                }
            }
        }
        }
    }
}