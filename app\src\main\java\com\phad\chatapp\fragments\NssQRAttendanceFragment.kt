package com.phad.chatapp.fragments

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.QrCode
import androidx.compose.material.icons.filled.Stop
import androidx.compose.material.icons.filled.People
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.foundation.clickable
import java.util.Date
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.phad.chatapp.R
import com.phad.chatapp.models.AttendanceEvent
import com.phad.chatapp.utils.SessionManager
import com.phad.chatapp.viewmodels.QRAttendanceViewModel
import com.phad.chatapp.viewmodels.QRAttendanceViewModelFactory
import kotlinx.coroutines.launch

/**
 * Fragment for Admin QR Attendance - Take Attendance functionality
 * Only accessible to Admin1 and Admin2 users in NSS interface
 */
class NssQRAttendanceFragment : Fragment() {
    private val TAG = "NssQRAttendanceFragment"
    
    private lateinit var viewModel: QRAttendanceViewModel
    private lateinit var sessionManager: SessionManager
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        sessionManager = SessionManager(requireContext())
        
        // Check if user is admin
        val userType = sessionManager.fetchUserType()
        Log.d(TAG, "User type from SessionManager: '$userType'")

        // Check for Admin1, Admin2, or Admin (consistent with ViewModel logic)
        if (userType != "Admin1" && userType != "Admin2" && userType != "Admin") {
            Log.w(TAG, "Non-admin user trying to access QR attendance: '$userType'")
            Toast.makeText(requireContext(), "Access denied. Admin privileges required.", Toast.LENGTH_LONG).show()
            // Navigate back or close fragment
            parentFragmentManager.popBackStack()
            return
        }

        Log.d(TAG, "Admin access granted for user type: '$userType'")
        
        // Initialize ViewModel
        val factory = QRAttendanceViewModelFactory(requireActivity().application)
        viewModel = ViewModelProvider(this, factory)[QRAttendanceViewModel::class.java]
        
        Log.d(TAG, "NssQRAttendanceFragment created for admin: ${sessionManager.fetchUserName()}")
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                val uiState by viewModel.adminUiState.collectAsState()
                
                QRAttendanceAdminScreen(
                    uiState = uiState,
                    onEventSelected = { event ->
                        viewModel.startAttendanceSession(event)
                    },
                    onEndSession = {
                        viewModel.endAttendanceSession()
                    },
                    onLoadEvents = {
                        viewModel.loadAvailableEvents()
                    },
                    onClearError = {
                        viewModel.clearError()
                    },
                    onCreateEvent = { name, description, date, openingTime, closingTime ->
                        viewModel.createAttendanceEvent(name, description, date, openingTime, closingTime)
                    },
                    onShowCreateDialog = {
                        viewModel.showCreateEventDialog()
                    },
                    onHideCreateDialog = {
                        viewModel.hideCreateEventDialog()
                    },
                    onClearCreateSuccess = {
                        viewModel.clearCreateEventSuccess()
                    },
                    onCloseEvent = { event ->
                        viewModel.closeEvent(event)
                    }
                )
            }
        }
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Force refresh user info to ensure proper admin status
        viewModel.refreshUserInfo()

        // Load available events when fragment is created
        viewModel.loadAvailableEvents()

        // Observe error messages and UI state changes
        lifecycleScope.launch {
            viewModel.adminUiState.collect { state ->
                Log.d(TAG, "UI State changed: isAdmin=${state.isAdmin}, isSessionActive=${state.isSessionActive}, adminId='${state.adminId}', adminName='${state.adminName}'")

                state.errorMessage?.let { error ->
                    Toast.makeText(requireContext(), error, Toast.LENGTH_LONG).show()
                    viewModel.clearError()
                }
            }
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        // End any active session when leaving the fragment
        lifecycleScope.launch {
            val currentState = viewModel.adminUiState.value
            if (currentState.isSessionActive) {
                viewModel.endAttendanceSession()
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun QRAttendanceAdminScreen(
    uiState: com.phad.chatapp.viewmodels.AdminQRUiState,
    onEventSelected: (AttendanceEvent) -> Unit,
    onEndSession: () -> Unit,
    onLoadEvents: () -> Unit,
    onClearError: () -> Unit,
    onCreateEvent: (String, String, Date, Date, Date) -> Unit,
    onShowCreateDialog: () -> Unit,
    onHideCreateDialog: () -> Unit,
    onClearCreateSuccess: () -> Unit,
    onCloseEvent: (AttendanceEvent) -> Unit
) {
    // Handle success message
    val context = LocalContext.current
    LaunchedEffect(uiState.createEventSuccess) {
        if (uiState.createEventSuccess) {
            // Show success toast
            Toast.makeText(
                context,
                "Event created successfully",
                Toast.LENGTH_SHORT
            ).show()
            onClearCreateSuccess()
        }
    }

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        floatingActionButton = {
            // Debug logging for FAB visibility
            Log.d("QRAttendanceUI", "FAB visibility check: isSessionActive=${uiState.isSessionActive}, isAdmin=${uiState.isAdmin}")
            Log.d("QRAttendanceUI", "FAB should show: ${!uiState.isSessionActive && uiState.isAdmin}")

            // TEMPORARY: Always show FAB for testing
            // TODO: Remove this and use the proper condition below
            FloatingActionButton(
                onClick = onShowCreateDialog,
                containerColor = Color(0xFF2196F3),
                contentColor = Color.White
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "Create New Event"
                )
            }

            // Original condition (commented out for testing)
            /*
            // Show FAB only when not in active session and user is admin
            if (!uiState.isSessionActive && uiState.isAdmin) {
                Log.d("QRAttendanceUI", "Showing FloatingActionButton")
                FloatingActionButton(
                    onClick = onShowCreateDialog,
                    containerColor = Color(0xFF2196F3),
                    contentColor = Color.White
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "Create New Event"
                    )
                }
            } else {
                Log.d("QRAttendanceUI", "FloatingActionButton hidden - isSessionActive=${uiState.isSessionActive}, isAdmin=${uiState.isAdmin}")
            }
            */
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(Color(0xFFF5F5F5))
                .padding(paddingValues)
                .padding(16.dp)
        ) {
            // Header
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(containerColor = Color(0xFF2196F3))
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        imageVector = Icons.Default.QrCode,
                        contentDescription = "QR Attendance",
                        tint = Color.White,
                        modifier = Modifier.size(32.dp)
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "QR Attendance - Admin",
                        color = Color.White,
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = "Admin: ${uiState.adminName}",
                        color = Color.White.copy(alpha = 0.9f),
                        fontSize = 14.sp
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            if (uiState.isLoading) {
                // Loading state
                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            } else if (!uiState.isSessionActive) {
                // Event selection screen
                EventSelectionScreen(
                    events = uiState.availableEvents,
                    onEventSelected = onEventSelected,
                    onRefresh = onLoadEvents,
                    onCloseEvent = onCloseEvent
                )
            } else {
                // Active session screen
                ActiveSessionScreen(
                    uiState = uiState,
                    onEndSession = onEndSession
                )
            }
        }
    }

    // Create Event Dialog
    if (uiState.showCreateEventDialog) {
        CreateEventDialog(
            isCreating = uiState.isCreatingEvent,
            onCreateEvent = onCreateEvent,
            onDismiss = onHideCreateDialog,
            errorMessage = uiState.errorMessage
        )
    }
}

@Composable
fun EventSelectionScreen(
    events: List<AttendanceEvent>,
    onEventSelected: (AttendanceEvent) -> Unit,
    onRefresh: () -> Unit,
    onCloseEvent: ((AttendanceEvent) -> Unit)? = null
) {
    Column {
        // Header with refresh button
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Select Event for Attendance",
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold
            )
            
            IconButton(onClick = onRefresh) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = "Refresh Events"
                )
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        if (events.isEmpty()) {
            // No events available
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(containerColor = Color(0xFFFFF3E0))
            ) {
                Column(
                    modifier = Modifier.padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "No Active Events",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "There are no live events available for attendance. Please check back later or create a new event.",
                        fontSize = 14.sp,
                        textAlign = TextAlign.Center,
                        color = Color.Gray
                    )
                }
            }
        } else {
            // Events list
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(events) { event ->
                    EventCard(
                        event = event,
                        onSelect = { onEventSelected(event) },
                        onCloseEvent = onCloseEvent
                    )
                }
            }
        }
    }
}

@Composable
fun EventCard(
    event: AttendanceEvent,
    onSelect: () -> Unit,
    onCloseEvent: ((AttendanceEvent) -> Unit)? = null
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = event.getEventName(),
                fontSize = 16.sp,
                fontWeight = FontWeight.SemiBold
            )
            
            if (event.description.isNotEmpty()) {
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = event.description,
                    fontSize = 14.sp,
                    color = Color.Gray
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "Event Date: ${event.getFormattedEventDate()}",
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                    Text(
                        text = "Time: ${event.getFormattedTimeRange()}",
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                    Text(
                        text = "Total Marked: ${event.totalMarked}",
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                }

                Column {
                    Button(
                        onClick = onSelect,
                        colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF4CAF50))
                    ) {
                        Text("Start Attendance")
                    }

                    // Show close button only if event is live and callback is provided
                    if (event.getEventStatus() == AttendanceEvent.STATUS_LIVE && onCloseEvent != null) {
                        Spacer(modifier = Modifier.height(4.dp))
                        Button(
                            onClick = { onCloseEvent(event) },
                            colors = ButtonDefaults.buttonColors(containerColor = Color(0xFFFF5722))
                        ) {
                            Text("Close Event")
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun ActiveSessionScreen(
    uiState: com.phad.chatapp.viewmodels.AdminQRUiState,
    onEndSession: () -> Unit
) {
    Column {
        // Session info header
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color(0xFF4CAF50))
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Active Session",
                    color = Color.White,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = uiState.selectedEvent?.getEventName() ?: "Unknown Event",
                    color = Color.White.copy(alpha = 0.9f),
                    fontSize = 14.sp
                )

                Spacer(modifier = Modifier.height(8.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Icon(
                            imageVector = Icons.Default.People,
                            contentDescription = "Attendees",
                            tint = Color.White,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = "Attendees: ${uiState.attendeeCount}",
                            color = Color.White,
                            fontSize = 14.sp
                        )
                    }

                    Text(
                        text = "QR Refreshed: ${uiState.qrRefreshCount} times",
                        color = Color.White.copy(alpha = 0.8f),
                        fontSize = 12.sp
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // QR Code display
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Scan QR Code for Attendance",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.height(16.dp))

                // QR Code Image
                uiState.currentQRCode?.let { bitmap ->
                    Image(
                        bitmap = bitmap.asImageBitmap(),
                        contentDescription = "QR Code for Attendance",
                        modifier = Modifier.size(250.dp)
                    )
                } ?: run {
                    // Loading placeholder
                    Box(
                        modifier = Modifier.size(250.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // QR Code info
                uiState.currentQRData?.let { qrData ->
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "QR Code ID: ${qrData.qrId.take(8)}...",
                            fontSize = 12.sp,
                            color = Color.Gray
                        )
                        Text(
                            text = "Valid for: ${qrData.getRemainingValiditySeconds()}s",
                            fontSize = 12.sp,
                            color = Color.Gray
                        )
                        Text(
                            text = "Generated: ${qrData.getFormattedTimestamp()}",
                            fontSize = 12.sp,
                            color = Color.Gray
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Auto-refresh indicator
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = "Auto Refresh",
                        tint = Color(0xFF2196F3),
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "QR code refreshes every 2 seconds",
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // End session button
        Button(
            onClick = onEndSession,
            modifier = Modifier.fillMaxWidth(),
            colors = ButtonDefaults.buttonColors(containerColor = Color(0xFFF44336)),
            shape = RoundedCornerShape(8.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Stop,
                contentDescription = "End Session",
                modifier = Modifier.size(20.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "End Attendance Session",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CreateEventDialog(
    isCreating: Boolean,
    onCreateEvent: (String, String, Date, Date, Date) -> Unit,
    onDismiss: () -> Unit,
    errorMessage: String?
) {
    var eventName by remember { mutableStateOf("") }
    var eventDescription by remember { mutableStateOf("") }
    var selectedDate by remember { mutableStateOf(Date()) }
    var openingTime by remember {
        mutableStateOf(com.phad.chatapp.utils.AttendanceEventUtils.createTimeFromHourMinute(Date(), 9, 0))
    }
    var closingTime by remember {
        mutableStateOf(com.phad.chatapp.utils.AttendanceEventUtils.createTimeFromHourMinute(Date(), 17, 0))
    }
    var showDatePicker by remember { mutableStateOf(false) }
    var showOpeningTimePicker by remember { mutableStateOf(false) }
    var showClosingTimePicker by remember { mutableStateOf(false) }
    var showError by remember { mutableStateOf(false) }
    var validationErrorMessage by remember { mutableStateOf("") }

    // Reset error state when dialog opens
    LaunchedEffect(Unit) {
        showError = false
    }

    // Show error if there's an error message
    LaunchedEffect(errorMessage) {
        showError = !errorMessage.isNullOrEmpty()
    }

    AlertDialog(
        onDismissRequest = {
            if (!isCreating) {
                onDismiss()
            }
        },
        title = {
            Text(
                text = "Create New Event",
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column {
                // Event Name Field
                OutlinedTextField(
                    value = eventName,
                    onValueChange = {
                        eventName = it
                        showError = false
                    },
                    label = { Text("Event Name *") },
                    placeholder = { Text("Enter event name") },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !isCreating,
                    isError = showError && eventName.trim().isEmpty(),
                    supportingText = {
                        if (showError && eventName.trim().isEmpty()) {
                            Text(
                                text = "Event name is required",
                                color = MaterialTheme.colorScheme.error
                            )
                        }
                    }
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Event Date Field
                OutlinedTextField(
                    value = java.text.SimpleDateFormat("dd MMM yyyy", java.util.Locale.getDefault()).format(selectedDate),
                    onValueChange = { },
                    label = { Text("Event Date") },
                    placeholder = { Text("Select event date") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { showDatePicker = true },
                    enabled = false,
                    readOnly = true,
                    trailingIcon = {
                        Icon(
                            imageVector = Icons.Default.DateRange,
                            contentDescription = "Select Date"
                        )
                    }
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Time Pickers Row
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // Opening Time Field
                    OutlinedTextField(
                        value = com.phad.chatapp.utils.AttendanceEventUtils.formatTimeForPicker(openingTime),
                        onValueChange = { },
                        label = { Text("Opening Time") },
                        placeholder = { Text("Select opening time") },
                        modifier = Modifier
                            .weight(1f)
                            .clickable { showOpeningTimePicker = true },
                        enabled = false,
                        readOnly = true,
                        trailingIcon = {
                            Icon(
                                imageVector = Icons.Default.DateRange,
                                contentDescription = "Select Opening Time"
                            )
                        }
                    )

                    // Closing Time Field
                    OutlinedTextField(
                        value = com.phad.chatapp.utils.AttendanceEventUtils.formatTimeForPicker(closingTime),
                        onValueChange = { },
                        label = { Text("Closing Time") },
                        placeholder = { Text("Select closing time") },
                        modifier = Modifier
                            .weight(1f)
                            .clickable { showClosingTimePicker = true },
                        enabled = false,
                        readOnly = true,
                        trailingIcon = {
                            Icon(
                                imageVector = Icons.Default.DateRange,
                                contentDescription = "Select Closing Time"
                            )
                        }
                    )
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Event Description Field
                OutlinedTextField(
                    value = eventDescription,
                    onValueChange = { eventDescription = it },
                    label = { Text("Description (Optional)") },
                    placeholder = { Text("Enter event description") },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !isCreating,
                    minLines = 2,
                    maxLines = 4
                )

                // Error message
                if (showError) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = if (validationErrorMessage.isNotEmpty()) validationErrorMessage else (errorMessage ?: "Unknown error"),
                        color = MaterialTheme.colorScheme.error,
                        fontSize = 14.sp
                    )
                }

                // Loading indicator
                if (isCreating) {
                    Spacer(modifier = Modifier.height(16.dp))
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(20.dp),
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "Creating event...",
                            fontSize = 14.sp,
                            color = Color.Gray
                        )
                    }
                }
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    val trimmedName = eventName.trim()
                    when {
                        trimmedName.isEmpty() -> {
                            showError = true
                            validationErrorMessage = "Event name is required"
                        }
                        !com.phad.chatapp.utils.AttendanceEventUtils.validateEventTimes(openingTime, closingTime) -> {
                            showError = true
                            validationErrorMessage = "Closing time must be after opening time"
                        }
                        !com.phad.chatapp.utils.AttendanceEventUtils.validateOpeningTimeNotInPast(selectedDate, openingTime) -> {
                            showError = true
                            validationErrorMessage = "Opening time cannot be in the past"
                        }
                        else -> {
                            showError = false
                            validationErrorMessage = ""
                            onCreateEvent(trimmedName, eventDescription.trim(), selectedDate, openingTime, closingTime)
                        }
                    }
                },
                enabled = !isCreating,
                colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF2196F3))
            ) {
                Text("Create Event")
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismiss,
                enabled = !isCreating
            ) {
                Text("Cancel")
            }
        }
    )

    // Date Picker Dialog
    if (showDatePicker) {
        val context = LocalContext.current
        LaunchedEffect(showDatePicker) {
            val calendar = java.util.Calendar.getInstance()
            calendar.time = selectedDate

            val datePickerDialog = android.app.DatePickerDialog(
                context,
                { _, year, month, dayOfMonth ->
                    val newCalendar = java.util.Calendar.getInstance()
                    newCalendar.set(year, month, dayOfMonth)
                    selectedDate = newCalendar.time

                    // Time objects don't need to be updated when date changes
                    // since we now store date and time separately

                    showDatePicker = false
                },
                calendar.get(java.util.Calendar.YEAR),
                calendar.get(java.util.Calendar.MONTH),
                calendar.get(java.util.Calendar.DAY_OF_MONTH)
            )

            datePickerDialog.setOnDismissListener {
                showDatePicker = false
            }

            datePickerDialog.show()
        }
    }

    // Opening Time Picker Dialog
    if (showOpeningTimePicker) {
        val context = LocalContext.current
        LaunchedEffect(showOpeningTimePicker) {
            val timePickerDialog = android.app.TimePickerDialog(
                context,
                { _, hourOfDay, minute ->
                    openingTime = com.phad.chatapp.utils.AttendanceEventUtils.createTimeFromHourMinute(
                        Date(), hourOfDay, minute
                    )
                    showOpeningTimePicker = false
                },
                com.phad.chatapp.utils.AttendanceEventUtils.getHourFromDate(openingTime),
                com.phad.chatapp.utils.AttendanceEventUtils.getMinuteFromDate(openingTime),
                false // Use 12-hour format
            )

            timePickerDialog.setOnDismissListener {
                showOpeningTimePicker = false
            }

            timePickerDialog.show()
        }
    }

    // Closing Time Picker Dialog
    if (showClosingTimePicker) {
        val context = LocalContext.current
        LaunchedEffect(showClosingTimePicker) {
            val timePickerDialog = android.app.TimePickerDialog(
                context,
                { _, hourOfDay, minute ->
                    closingTime = com.phad.chatapp.utils.AttendanceEventUtils.createTimeFromHourMinute(
                        Date(), hourOfDay, minute
                    )
                    showClosingTimePicker = false
                },
                com.phad.chatapp.utils.AttendanceEventUtils.getHourFromDate(closingTime),
                com.phad.chatapp.utils.AttendanceEventUtils.getMinuteFromDate(closingTime),
                false // Use 12-hour format
            )

            timePickerDialog.setOnDismissListener {
                showClosingTimePicker = false
            }

            timePickerDialog.show()
        }
    }
}
